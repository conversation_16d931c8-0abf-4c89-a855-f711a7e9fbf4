import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';

// ===== INTERFACES =====
export interface Department {
  id: string;
  name: string;
  code?: string;
  description?: string;
  isActive?: boolean;
}

export interface DepartmentCategoryMapping {
  departmentId: string;
  departmentName: string;
  categories: string[];
}

@Injectable({
  providedIn: 'root'
})
export class DepartmentService {
  private readonly engineUrl = environment.engineUrl;

  // Session-level storage for department-category mappings
  private sessionMappings: Map<string, DepartmentCategoryMapping[]> = new Map();

  constructor(private readonly http: HttpClient) {}

  // ===== DEPARTMENT METHODS =====
  /**
   * Get all departments for a tenant (via secure backend endpoint)
   */
  getDepartments(tenantId: string): Observable<Department[]> {
    return this.http.get<any>(`${this.engineUrl}api/smart-dashboard/departments/${tenantId}`)
      .pipe(
        map(response => {
          if (response.status === 'success' && response.data) {
            const departments = response.data.map((dept: any) => ({
              id: dept.id,
              name: dept.name,
              code: dept.code,
              description: dept.description,
              isActive: dept.isActive !== false
            }));
            return departments;
          }
          throw new Error(response.message || 'Failed to fetch departments');
        }),
        catchError(error => {
          console.error('DepartmentService: Error fetching departments:', error);
          return throwError(() => new Error('Failed to fetch departments'));
        })
      );
  }

  // ===== DEPARTMENT-CATEGORY MAPPING METHODS =====
  /**
   * Get department-category mappings for a tenant (session-level)
   */
  getDepartmentCategoryMappings(tenantId: string): Observable<DepartmentCategoryMapping[]> {
    // Return session mappings if they exist, otherwise return empty array
    const mappings = this.sessionMappings.get(tenantId) || [];

    return new Observable(observer => {
      observer.next(mappings);
      observer.complete();
    });
  }

  /**
   * Save department-category mappings (session-level)
   */
  saveDepartmentCategoryMappings(tenantId: string, mappings: DepartmentCategoryMapping[]): Observable<boolean> {
    // Store mappings in session storage
    this.sessionMappings.set(tenantId, mappings);

    return new Observable(observer => {
      observer.next(true);
      observer.complete();
    });
  }

  /**
   * Get categories mapped to a specific department (session-level)
   */
  getCategoriesForDepartment(tenantId: string, departmentId: string): Observable<string[]> {
    return this.getDepartmentCategoryMappings(tenantId).pipe(
      map(mappings => {
        const mapping = mappings.find(m => m.departmentId === departmentId);
        return mapping ? mapping.categories : [];
      })
    );
  }

  /**
   * Get department for a specific category (session-level)
   */
  getDepartmentForCategory(tenantId: string, category: string): Observable<string | null> {
    return this.getDepartmentCategoryMappings(tenantId).pipe(
      map(mappings => {
        const mapping = mappings.find(m => m.categories.includes(category));
        return mapping ? mapping.departmentId : null;
      })
    );
  }

  /**
   * Clear session mappings for a tenant
   */
  clearSessionMappings(tenantId: string): void {
    this.sessionMappings.delete(tenantId);
  }

  /**
   * Check if session mappings exist for a tenant
   */
  hasSessionMappings(tenantId: string): boolean {
    return this.sessionMappings.has(tenantId);
  }

  // ===== UTILITY METHODS =====

  /**
   * Validate department-category mapping rules
   * - Single department can have multiple categories
   * - Single category can only be mapped to one department
   */
  validateMappings(mappings: DepartmentCategoryMapping[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const categoryToDepartment = new Map<string, string>();

    for (const mapping of mappings) {
      for (const category of mapping.categories) {
        if (categoryToDepartment.has(category)) {
          const existingDept = categoryToDepartment.get(category);
          if (existingDept !== mapping.departmentId) {
            errors.push(`Category "${category}" is mapped to multiple departments: "${existingDept}" and "${mapping.departmentName}"`);
          }
        } else {
          categoryToDepartment.set(category, mapping.departmentId);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

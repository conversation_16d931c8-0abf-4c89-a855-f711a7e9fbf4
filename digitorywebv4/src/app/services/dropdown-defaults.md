# Dropdown Default Selection Service

## Overview

The `DropdownDefaultsService` provides a consistent way to handle default selections for dropdown components across the application. It implements the following behavior:

- **Multi-select dropdowns**: Select all available options by default
- **Single-select dropdowns**: Select the first available option by default

## Usage

### Basic Usage

```typescript
import { DropdownDefaultsService } from '../services/dropdown-defaults.service';

constructor(private dropdownDefaults: DropdownDefaultsService) {}

// For multi-select dropdown
this.dropdownDefaults.setDefaultSelection(
  this.myFormControl,
  this.availableOptions,
  true // isMultiSelect
);

// For single-select dropdown
this.dropdownDefaults.setDefaultSelection(
  this.myFormControl,
  this.availableOptions,
  false // isMultiSelect
);
```

### Specialized Methods

#### Location Selection
```typescript
// Multi-select locations (e.g., for inventory dashboard)
this.dropdownDefaults.setDefaultLocationSelection(
  this.selectedLocationsCtrl,
  this.branches,
  true // isMultiSelect
);

// Single-select location (e.g., for reconciliation dashboard)
this.dropdownDefaults.setDefaultLocationSelection(
  this.selectedLocationsCtrl,
  this.branches,
  false // isMultiSelect
);
```

#### Category Selection
```typescript
// Categories are always multi-select
this.dropdownDefaults.setDefaultCategorySelection(
  this.selectedCategoriesCtrl,
  this.categories
);
```

#### Department Selection
```typescript
// Departments are always multi-select
this.dropdownDefaults.setDefaultDepartmentSelection(
  this.selectedDepartmentsCtrl,
  this.departments
);
```

#### Work Area Selection
```typescript
// Multi-select work areas with flattening
this.dropdownDefaults.setDefaultWorkAreaSelection(
  this.selectedWorkAreasCtrl,
  this.workAreas,
  true, // isMultiSelect
  true  // flattenWorkAreas (for nested branch structure)
);
```

### Utility Methods

#### Check if all options are selected
```typescript
const allSelected = this.dropdownDefaults.areAllOptionsSelected(
  this.myFormControl,
  this.totalOptionsCount
);
```

#### Toggle select all/deselect all
```typescript
this.dropdownDefaults.toggleSelectAll(
  this.myFormControl,
  this.allOptions
);
```

## Implementation Examples

### Smart Dashboard Component

The smart dashboard component uses this service to set defaults for:
- **Locations**: Multi-select for inventory dashboard, single-select for reconciliation
- **Categories**: Always multi-select, select all by default
- **Subcategories**: Always multi-select, select all by default
- **Work Areas**: Multi-select for inventory, no default for reconciliation
- **Departments**: Always multi-select, select all by default

### Subrecipe Master Component

The subrecipe master component uses this pattern for:
- **Prepared At**: Multi-select, select all locations by default
- **Used At Outlet**: Multi-select, select all outlets by default

### Menu Master Component

The menu master component uses this pattern for:
- **Restaurant**: Single-select, select first restaurant by default
- **Prepared At**: Multi-select, select all locations by default
- **Used Outlet**: Multi-select, select all outlets by default

## Benefits

1. **Consistency**: All dropdowns across the application follow the same default selection pattern
2. **User Experience**: Users see relevant data immediately without having to manually select options
3. **Maintainability**: Centralized logic for default selections makes it easy to update behavior
4. **Flexibility**: Service supports both multi-select and single-select scenarios
5. **Type Safety**: TypeScript interfaces ensure proper usage

## Migration Guide

To update existing components to use this service:

1. Import the service in your component
2. Inject it in the constructor
3. Replace manual default selection logic with service calls
4. Use the appropriate method based on your dropdown type

Example migration:
```typescript
// Before
if (this.branches.length > 0) {
  this.selectedLocationsCtrl.setValue(this.branches.map(b => b.restaurantIdOld));
}

// After
this.dropdownDefaults.setDefaultLocationSelection(
  this.selectedLocationsCtrl,
  this.branches,
  true
);
```

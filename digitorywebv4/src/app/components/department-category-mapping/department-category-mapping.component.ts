import { Component, OnInit, OnDestroy, OnChanges, SimpleChanges, Input, Output, EventEmitter, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

// Material modules
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';

// Services
import { DepartmentService, Department, DepartmentCategoryMapping } from '../../services/department.service';
import { SmartDashboardService } from '../../services/smart-dashboard.service';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-department-category-mapping',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatDividerModule
  ],
  templateUrl: './department-category-mapping.component.html',
  styleUrls: ['./department-category-mapping.component.scss']
})
export class DepartmentCategoryMappingComponent implements OnInit, OnChanges, OnDestroy {
  @Input() tenantId: string = '';
  @Input() showAsDialog: boolean = false;
  @Input() departments: Department[] = [];
  @Input() selectedDepartmentIds: string[] = [];
  @Input() categories: string[] = [];
  @Input() existingMappings: DepartmentCategoryMapping[] = [];
  @Output() mappingsChanged = new EventEmitter<DepartmentCategoryMapping[]>();
  @Output() closeDialog = new EventEmitter<void>();

  // Form and data
  mappingForm: FormGroup;
  mappings: DepartmentCategoryMapping[] = [];
  selectedDepartments: Department[] = [];

  // UI state
  isLoading = false;
  isSaving = false;
  validationErrors: string[] = [];
  
  // Destroy subject for cleanup
  private destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private departmentService: DepartmentService,
    private smartDashboardService: SmartDashboardService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    if (!this.tenantId) {
      const user = this.authService.getCurrentUser();
      this.tenantId = user?.tenantId || '';
    }

    // Use input properties if provided, otherwise load data
    if (this.departments.length > 0 && this.categories.length > 0) {
      this.updateSelectedDepartments();
      this.mappings = this.existingMappings || [];
      this.buildFormFromMappings();
    } else if (this.tenantId) {
      this.loadData();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedDepartmentIds'] && !changes['selectedDepartmentIds'].firstChange) {
      this.updateSelectedDepartments();
      this.buildFormFromSelectedDepartments();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // ===== INITIALIZATION =====
  private initializeForm(): void {
    this.mappingForm = this.fb.group({
      mappings: this.fb.array([])
    });
  }

  get mappingsFormArray(): FormArray {
    return this.mappingForm.get('mappings') as FormArray;
  }

  // ===== DATA LOADING =====
  private loadData(): void {
    this.isLoading = true;
    this.loadDepartments();
    this.loadCategories();
    this.loadExistingMappings();
  }

  private loadDepartments(): void {
    this.departmentService.getDepartments(this.tenantId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (departments) => {
          this.departments = departments;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showError('Failed to load departments');
        }
      });
  }

  private loadCategories(): void {
    this.smartDashboardService.getCategories(this.tenantId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response && response.categories) {
            this.categories = response.categories.map((cat: any) => cat.name || cat.categoryName);
          }
          this.cdr.detectChanges();
        },
        error: () => {
          this.showError('Failed to load categories');
        }
      });
  }

  private loadExistingMappings(): void {
    this.departmentService.getDepartmentCategoryMappings(this.tenantId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (mappings) => {
          this.mappings = mappings;
          this.buildFormFromMappings();
          this.isLoading = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.cdr.detectChanges();
        }
      });
  }

  // ===== FORM MANAGEMENT =====
  private buildFormFromMappings(): void {
    // If we have selected departments, use them
    if (this.selectedDepartmentIds.length > 0) {
      this.buildFormFromSelectedDepartments();
      return;
    }

    // If no departments are selected, clear the form array (empty right side)
    const mappingsArray = this.mappingsFormArray;
    mappingsArray.clear();
    this.cdr.detectChanges();
  }

  private createMappingFormGroup(mapping: DepartmentCategoryMapping): FormGroup {
    return this.fb.group({
      departmentId: [mapping.departmentId, Validators.required],
      departmentName: [mapping.departmentName, Validators.required],
      categories: [mapping.categories || []]
    });
  }

  // ===== SELECTED DEPARTMENTS MANAGEMENT =====
  private updateSelectedDepartments(): void {
    this.selectedDepartments = this.departments.filter(dept =>
      this.selectedDepartmentIds.includes(dept.id)
    );
  }

  private buildFormFromSelectedDepartments(): void {
    // Store current form values to preserve user selections
    const currentFormValues = new Map<string, string[]>();
    for (let i = 0; i < this.mappingsFormArray.length; i++) {
      const formGroup = this.mappingsFormArray.at(i) as FormGroup;
      const departmentId = formGroup.get('departmentId')?.value;
      const categories = formGroup.get('categories')?.value || [];
      if (departmentId) {
        currentFormValues.set(departmentId, categories);
      }
    }

    // Get currently selected department IDs for comparison
    const currentDepartmentIds = new Set(
      Array.from({ length: this.mappingsFormArray.length }, (_, i) => {
        const formGroup = this.mappingsFormArray.at(i) as FormGroup;
        return formGroup.get('departmentId')?.value;
      }).filter(Boolean)
    );

    const newDepartmentIds = new Set(this.selectedDepartments.map(d => d.id));

    // Remove departments that are no longer selected
    for (let i = this.mappingsFormArray.length - 1; i >= 0; i--) {
      const formGroup = this.mappingsFormArray.at(i) as FormGroup;
      const departmentId = formGroup.get('departmentId')?.value;
      if (!newDepartmentIds.has(departmentId)) {
        this.mappingsFormArray.removeAt(i);
      }
    }

    // Add new departments that were just selected
    this.selectedDepartments.forEach(dept => {
      if (!currentDepartmentIds.has(dept.id)) {
        // Find existing mapping for this department
        const existingMapping = this.mappings.find(m => m.departmentId === dept.id);

        // Check if we have current form values for this department (from previous selections)
        const preservedCategories = currentFormValues.get(dept.id) || [];

        const mapping: DepartmentCategoryMapping = existingMapping || {
          departmentId: dept.id,
          departmentName: dept.name,
          categories: preservedCategories // Preserve user selections or start empty
        };

        // If we have preserved categories, use them instead of existing mapping
        if (preservedCategories.length > 0) {
          mapping.categories = preservedCategories;
        }

        this.mappingsFormArray.push(this.createMappingFormGroup(mapping));
      }
    });

    // Sort form array to match the order of selected departments
    const sortedControls: FormGroup[] = [];
    this.selectedDepartments.forEach(dept => {
      const control = this.mappingsFormArray.controls.find(ctrl => {
        const formGroup = ctrl as FormGroup;
        return formGroup.get('departmentId')?.value === dept.id;
      }) as FormGroup;
      if (control) {
        sortedControls.push(control);
      }
    });

    // Clear and rebuild with sorted controls
    while (this.mappingsFormArray.length !== 0) {
      this.mappingsFormArray.removeAt(0);
    }
    sortedControls.forEach(control => {
      this.mappingsFormArray.push(control);
    });

    // Trigger change detection to ensure available categories are updated
    this.cdr.detectChanges();
  }

  // ===== FORM ACTIONS =====

  onCategoriesChange(index: number, categories: string[]): void {
    const mappingGroup = this.mappingsFormArray.at(index) as FormGroup;
    if (mappingGroup) {
      mappingGroup.patchValue({ categories });

      // Update the internal mappings array to keep it in sync
      const departmentId = mappingGroup.get('departmentId')?.value;
      if (departmentId) {
        const existingMappingIndex = this.mappings.findIndex(m => m.departmentId === departmentId);
        if (existingMappingIndex >= 0) {
          this.mappings[existingMappingIndex].categories = categories;
        } else {
          // Add new mapping if it doesn't exist
          const departmentName = mappingGroup.get('departmentName')?.value;
          this.mappings.push({
            departmentId,
            departmentName,
            categories
          });
        }
      }

      // Trigger change detection to update available categories for other departments
      // This ensures that categories selected by this department are no longer available to others
      this.cdr.detectChanges();

      this.validateMappings();
    }
  }

  // ===== VALIDATION =====
  private validateMappings(): void {
    const formMappings = this.mappingsFormArray.value as DepartmentCategoryMapping[];

    // Since we prevent category conflicts at the UI level, we only need to validate
    // for other business rules (like required fields, etc.)
    const validation = this.departmentService.validateMappings(formMappings);

    // Filter out category conflict errors since they're prevented by UI
    this.validationErrors = validation.errors.filter(error =>
      !error.toLowerCase().includes('mapped to multiple departments')
    );
  }

  // ===== SAVE FUNCTIONALITY =====
  saveMappings(): void {
    if (this.mappingForm.invalid) {
      this.showError('Please fix form errors before saving');
      return;
    }

    this.validateMappings();
    if (this.validationErrors.length > 0) {
      this.showError('Please fix validation errors before saving');
      return;
    }

    this.isSaving = true;
    const mappings = this.mappingsFormArray.value as DepartmentCategoryMapping[];
    
    // Filter out empty mappings
    const validMappings = mappings.filter(m => 
      m.departmentId && m.departmentName && m.categories.length > 0
    );

    this.departmentService.saveDepartmentCategoryMappings(this.tenantId, validMappings)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (success) => {
          if (success) {
            this.mappings = validMappings;
            this.mappingsChanged.emit(validMappings);
            this.showSuccess('Mappings saved successfully');
          } else {
            this.showError('Failed to save mappings');
          }
          this.isSaving = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showError('Failed to save mappings');
          this.isSaving = false;
          this.cdr.detectChanges();
        }
      });
  }

  // ===== UTILITY METHODS =====
  getDepartmentName(departmentId: string): string {
    const department = this.departments.find(d => d.id === departmentId);
    return department ? department.name : 'Unknown Department';
  }

  getCategoriesPlaceholder(index: number): string {
    if (index >= this.mappingsFormArray.length) {
      return 'Select Categories';
    }

    const mappingGroup = this.mappingsFormArray.at(index) as FormGroup;
    if (!mappingGroup) {
      return 'Select Categories';
    }

    const selectedCategories = mappingGroup.get('categories')?.value || [];

    if (selectedCategories.length === 0) {
      return 'Select Categories';
    } else if (selectedCategories.length === 1) {
      return selectedCategories[0];
    } else if (selectedCategories.length <= 3) {
      return selectedCategories.join(', ');
    } else {
      return `${selectedCategories.length} categories selected`;
    }
  }

  getAvailableCategories(currentIndex: number): string[] {
    if (!this.categories || this.categories.length === 0) {
      return [];
    }

    // Get all categories that are already selected by OTHER departments
    const categoriesSelectedByOthers = new Set<string>();

    for (let i = 0; i < this.mappingsFormArray.length; i++) {
      if (i !== currentIndex) { // Skip current department
        const formGroup = this.mappingsFormArray.at(i) as FormGroup;
        const selectedCategories = formGroup.get('categories')?.value || [];
        selectedCategories.forEach((category: string) => {
          categoriesSelectedByOthers.add(category);
        });
      }
    }

    // Return only categories that are not selected by other departments
    return this.categories.filter(category => !categoriesSelectedByOthers.has(category));
  }

  // ===== ADDITIONAL UTILITY METHODS =====

  /**
   * Check if a department is currently selected
   */
  isDepartmentSelected(departmentId: string): boolean {
    return this.selectedDepartmentIds.includes(departmentId);
  }

  /**
   * Get current mapping for a specific department
   */
  getCurrentMapping(departmentId: string): DepartmentCategoryMapping | null {
    const formIndex = this.mappingsFormArray.controls.findIndex(ctrl => {
      const formGroup = ctrl as FormGroup;
      return formGroup.get('departmentId')?.value === departmentId;
    });

    if (formIndex >= 0) {
      const formGroup = this.mappingsFormArray.at(formIndex) as FormGroup;
      return {
        departmentId: formGroup.get('departmentId')?.value,
        departmentName: formGroup.get('departmentName')?.value,
        categories: formGroup.get('categories')?.value || []
      };
    }

    return null;
  }

  /**
   * Check if there are any unsaved changes
   */
  hasUnsavedChanges(): boolean {
    const currentFormMappings = this.mappingsFormArray.value as DepartmentCategoryMapping[];

    // Compare with saved mappings
    if (currentFormMappings.length !== this.mappings.length) {
      return true;
    }

    return currentFormMappings.some(formMapping => {
      const savedMapping = this.mappings.find(m => m.departmentId === formMapping.departmentId);
      if (!savedMapping) {
        return formMapping.categories.length > 0; // New mapping with categories
      }

      // Compare categories arrays
      const formCategories = [...(formMapping.categories || [])].sort();
      const savedCategories = [...(savedMapping.categories || [])].sort();

      return formCategories.length !== savedCategories.length ||
             formCategories.some((cat, index) => cat !== savedCategories[index]);
    });
  }

  // ===== UI HELPERS =====
  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  onClose(): void {
    this.closeDialog.emit();
  }

  // ===== GETTERS FOR TEMPLATE =====
  get hasValidationErrors(): boolean {
    return this.validationErrors.length > 0;
  }



  get canSave(): boolean {
    return !this.isSaving &&
           this.mappingForm.valid &&
           this.validationErrors.length === 0 &&
           this.mappingsFormArray.length > 0;
  }

  // ===== TRACK BY FUNCTIONS =====

  /**
   * TrackBy function for department mapping rows to improve performance
   */
  trackByDepartmentId(index: number, item: any): string {
    const formGroup = item as FormGroup;
    return formGroup.get('departmentId')?.value || index.toString();
  }

  /**
   * TrackBy function for category options
   */
  trackByCategory(_index: number, item: string): string {
    return item;
  }
}

import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { DepartmentCategoryMappingComponent } from './department-category-mapping.component';
import { DepartmentService, Department, DepartmentCategoryMapping } from '../../services/department.service';
import { SmartDashboardService } from '../../services/smart-dashboard.service';
import { AuthService } from '../../services/auth.service';

describe('DepartmentCategoryMappingComponent - Edge Cases', () => {
  let component: DepartmentCategoryMappingComponent;
  let fixture: ComponentFixture<DepartmentCategoryMappingComponent>;
  let mockDepartmentService: jasmine.SpyObj<DepartmentService>;
  let mockSmartDashboardService: jasmine.SpyObj<SmartDashboardService>;
  let mockAuthService: jasmine.SpyObj<AuthService>;

  const mockDepartments: Department[] = [
    { id: 'dept1', name: 'Food', description: 'Food Department' },
    { id: 'dept2', name: 'Tobacco', description: 'Tobacco Department' },
    { id: 'dept3', name: 'Beverages', description: 'Beverages Department' }
  ];

  const mockCategories = ['FOOD', 'BEVERAGES', 'TOBACCO', 'SNACKS'];

  const mockExistingMappings: DepartmentCategoryMapping[] = [
    { departmentId: 'dept1', departmentName: 'Food', categories: ['FOOD', 'SNACKS'] },
    { departmentId: 'dept2', departmentName: 'Tobacco', categories: ['TOBACCO'] }
  ];

  beforeEach(async () => {
    const departmentServiceSpy = jasmine.createSpyObj('DepartmentService', [
      'getDepartments', 
      'getDepartmentCategoryMappings', 
      'saveDepartmentCategoryMappings',
      'validateMappings'
    ]);
    const smartDashboardServiceSpy = jasmine.createSpyObj('SmartDashboardService', ['getCategories']);
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);

    await TestBed.configureTestingModule({
      imports: [
        DepartmentCategoryMappingComponent,
        ReactiveFormsModule,
        MatSnackBarModule,
        NoopAnimationsModule
      ],
      providers: [
        { provide: DepartmentService, useValue: departmentServiceSpy },
        { provide: SmartDashboardService, useValue: smartDashboardServiceSpy },
        { provide: AuthService, useValue: authServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DepartmentCategoryMappingComponent);
    component = fixture.componentInstance;
    mockDepartmentService = TestBed.inject(DepartmentService) as jasmine.SpyObj<DepartmentService>;
    mockSmartDashboardService = TestBed.inject(SmartDashboardService) as jasmine.SpyObj<SmartDashboardService>;
    mockAuthService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;

    // Setup default mock returns
    mockAuthService.getCurrentUser.and.returnValue({ tenantId: 'test-tenant' });
    mockDepartmentService.getDepartments.and.returnValue(of(mockDepartments));
    mockSmartDashboardService.getCategories.and.returnValue(of({ categories: mockCategories.map(name => ({ name })) }));
    mockDepartmentService.getDepartmentCategoryMappings.and.returnValue(of(mockExistingMappings));
    mockDepartmentService.validateMappings.and.returnValue({ isValid: true, errors: [] });
    mockDepartmentService.saveDepartmentCategoryMappings.and.returnValue(of(true));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should preserve category selections when new departments are added', () => {
    // Setup initial state with input properties
    component.departments = mockDepartments;
    component.categories = mockCategories;
    component.existingMappings = mockExistingMappings;
    component.selectedDepartmentIds = ['dept1', 'dept2'];
    
    component.ngOnInit();
    fixture.detectChanges();

    // Verify initial form state
    expect(component.mappingsFormArray.length).toBe(2);
    
    // Simulate user changing categories for dept1
    component.onCategoriesChange(0, ['FOOD', 'BEVERAGES']);
    
    // Now add a new department
    component.selectedDepartmentIds = ['dept1', 'dept2', 'dept3'];
    component.ngOnChanges({
      selectedDepartmentIds: {
        currentValue: ['dept1', 'dept2', 'dept3'],
        previousValue: ['dept1', 'dept2'],
        firstChange: false,
        isFirstChange: () => false
      }
    });

    // Verify that the user's category selection for dept1 is preserved
    expect(component.mappingsFormArray.length).toBe(3);
    const dept1FormGroup = component.mappingsFormArray.at(0);
    expect(dept1FormGroup.get('categories')?.value).toEqual(['FOOD', 'BEVERAGES']);
  });

  it('should handle department removal without affecting other mappings', () => {
    // Setup initial state
    component.departments = mockDepartments;
    component.categories = mockCategories;
    component.existingMappings = mockExistingMappings;
    component.selectedDepartmentIds = ['dept1', 'dept2', 'dept3'];
    
    component.ngOnInit();
    fixture.detectChanges();

    // Verify initial state
    expect(component.mappingsFormArray.length).toBe(3);

    // Remove dept2
    component.selectedDepartmentIds = ['dept1', 'dept3'];
    component.ngOnChanges({
      selectedDepartmentIds: {
        currentValue: ['dept1', 'dept3'],
        previousValue: ['dept1', 'dept2', 'dept3'],
        firstChange: false,
        isFirstChange: () => false
      }
    });

    // Verify dept2 is removed but others remain
    expect(component.mappingsFormArray.length).toBe(2);
    const remainingDeptIds = component.mappingsFormArray.controls.map(ctrl => 
      ctrl.get('departmentId')?.value
    );
    expect(remainingDeptIds).toEqual(['dept1', 'dept3']);
  });

  it('should handle empty department selection gracefully', () => {
    // Setup initial state
    component.departments = mockDepartments;
    component.categories = mockCategories;
    component.selectedDepartmentIds = ['dept1', 'dept2'];
    
    component.ngOnInit();
    fixture.detectChanges();

    // Clear all departments
    component.selectedDepartmentIds = [];
    component.ngOnChanges({
      selectedDepartmentIds: {
        currentValue: [],
        previousValue: ['dept1', 'dept2'],
        firstChange: false,
        isFirstChange: () => false
      }
    });

    // Verify form is cleared
    expect(component.mappingsFormArray.length).toBe(0);
  });

  it('should maintain department order based on selection order', () => {
    // Setup initial state
    component.departments = mockDepartments;
    component.categories = mockCategories;
    component.selectedDepartmentIds = ['dept3', 'dept1', 'dept2']; // Different order
    
    component.ngOnInit();
    fixture.detectChanges();

    // Verify order matches selection order
    const formDeptIds = component.mappingsFormArray.controls.map(ctrl => 
      ctrl.get('departmentId')?.value
    );
    expect(formDeptIds).toEqual(['dept3', 'dept1', 'dept2']);
  });

  it('should handle invalid form group access gracefully', () => {
    component.departments = mockDepartments;
    component.categories = mockCategories;

    // Test getCategoriesPlaceholder with invalid index
    expect(component.getCategoriesPlaceholder(999)).toBe('Select Categories');

    // Test with empty form array
    expect(component.getCategoriesPlaceholder(0)).toBe('Select Categories');
  });

  it('should exclude categories selected by other departments', () => {
    // Setup initial state with two departments
    component.departments = mockDepartments;
    component.categories = mockCategories;
    component.selectedDepartmentIds = ['dept1', 'dept2'];

    component.ngOnInit();
    fixture.detectChanges();

    // Select categories for first department
    component.onCategoriesChange(0, ['FOOD', 'SNACKS']);

    // Check available categories for second department
    const availableForDept2 = component.getAvailableCategories(1);

    // Should not include FOOD and SNACKS since they're selected by dept1
    expect(availableForDept2).not.toContain('FOOD');
    expect(availableForDept2).not.toContain('SNACKS');
    expect(availableForDept2).toContain('BEVERAGES');
    expect(availableForDept2).toContain('TOBACCO');
  });

  it('should make categories available again when deselected', () => {
    // Setup initial state
    component.departments = mockDepartments;
    component.categories = mockCategories;
    component.selectedDepartmentIds = ['dept1', 'dept2'];

    component.ngOnInit();
    fixture.detectChanges();

    // Select categories for first department
    component.onCategoriesChange(0, ['FOOD']);

    // Verify FOOD is not available for second department
    let availableForDept2 = component.getAvailableCategories(1);
    expect(availableForDept2).not.toContain('FOOD');

    // Deselect FOOD from first department
    component.onCategoriesChange(0, []);

    // Verify FOOD is now available for second department
    availableForDept2 = component.getAvailableCategories(1);
    expect(availableForDept2).toContain('FOOD');
  });
});

<div class="smart-dashboard-container">
  <!-- Main Layout -->
  <div class="main-layout">
    <!-- Left Sidebar -->
    <div class="left-sidebar">
      <!-- Dashboard Selection -->
      <div class="dashboard-selection">
        <div class="filter-label">Dashboard</div>
        <mat-form-field appearance="outline" class="dashboard-dropdown">
          <mat-select [(value)]="selectedDashboard" [disabled]="!isConfigLoaded || dashboardTypes.length === 0"
            (selectionChange)="onDashboardChange()" placeholder="Select Dashboard">
            <mat-option *ngIf="!isConfigLoaded" disabled>Loading...</mat-option>
            <mat-option *ngIf="isConfigLoaded && dashboardTypes.length === 0" disabled>Service unavailable</mat-option>
            <mat-option *ngFor="let dashboardType of dashboardTypes" [value]="dashboardType.value">
              {{dashboardType.label}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <!-- Smart Filters Section -->
      <div class="filters-section">
        <h3 class="filters-title">
          <mat-icon>tune</mat-icon>
          Smart Filters
        </h3>

        <!-- Restaurants Filter -->
        <div class="filter-group">
          <div class="filter-label" *ngIf="selectedDashboard === 'reconciliation'">Restaurant</div>
          <div class="filter-label" *ngIf="selectedDashboard !== 'reconciliation'">Restaurants</div>

          <!-- Single Selection for Reconciliation Dashboard -->
          <mat-form-field *ngIf="selectedDashboard === 'reconciliation'" appearance="outline" class="filter-field">
            <mat-select [formControl]="selectedLocationsCtrl" placeholder="Select restaurant">
              <mat-option *ngFor="let branch of filteredBranches" [value]="branch.restaurantIdOld">
                {{branch.branchName}}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <!-- Multiple Selection for Other Dashboards -->
          <mat-form-field *ngIf="selectedDashboard !== 'reconciliation'" appearance="outline" class="filter-field">
            <mat-select [formControl]="selectedLocationsCtrl" multiple
              [placeholder]="'Select restaurants (' + (selectedLocationsCtrl.value?.length || 0) + '/' + filteredBranches.length + ')'">
              <mat-option>
                <ngx-mat-select-search [formControl]="locationFilterCtrl" placeholderLabel="Search locations..."
                  noEntriesFoundLabel="No locations found">
                </ngx-mat-select-search>
              </mat-option>
              <!-- Select All / Deselect All Options -->
              <div class="select-all-custom-option" (click)="toggleAllRestaurants($event)">
                <strong>{{areAllRestaurantsSelected() ? 'Deselect All' : 'Select All'}}</strong>
              </div>
              <mat-divider></mat-divider>
              <mat-option *ngFor="let branch of filteredBranches" [value]="branch.restaurantIdOld">
                {{branch.branchName}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>



        <!-- Categories Filter -->
        <div class="filter-group">
          <div class="filter-label">Categories</div>
          <mat-form-field appearance="outline" class="filter-field">
            <mat-select [formControl]="selectedCategoriesCtrl" multiple
              [placeholder]="'Select categories (' + (selectedCategoriesCtrl.value?.length || 0) + '/' + filteredCategories.length + ')'">
              <mat-option>
                <ngx-mat-select-search [formControl]="categoryFilterCtrl" placeholderLabel="Search categories..."
                  noEntriesFoundLabel="No categories found">
                </ngx-mat-select-search>
              </mat-option>
              <!-- Select All / Deselect All Options -->
              <div class="select-all-custom-option" (click)="toggleAllCategories($event)">
                <strong>{{areAllCategoriesSelected() ? 'Deselect All' : 'Select All'}}</strong>
              </div>
              <mat-divider></mat-divider>
              <mat-option *ngFor="let category of filteredCategories; trackBy: trackByIndex" [value]="category">
                {{category}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <!-- Subcategories Filter -->
        <div class="filter-group">
          <div class="filter-label">Subcategories</div>
          <mat-form-field appearance="outline" class="filter-field">
            <mat-select [formControl]="selectedSubcategoriesCtrl" multiple
              [placeholder]="'Select subcategories (' + (selectedSubcategoriesCtrl.value?.length || 0) + '/' + filteredSubcategories.length + ')'">
              <mat-option>
                <ngx-mat-select-search [formControl]="subcategoryFilterCtrl" placeholderLabel="Search subcategories..."
                  noEntriesFoundLabel="No subcategories found">
                </ngx-mat-select-search>
              </mat-option>
              <!-- Select All / Deselect All Options -->
              <div class="select-all-custom-option" (click)="toggleAllSubcategories($event)">
                <strong>{{areAllSubcategoriesSelected() ? 'Deselect All' : 'Select All'}}</strong>
              </div>
              <mat-divider></mat-divider>
              <mat-option *ngFor="let subcategory of filteredSubcategories; trackBy: trackByIndex"
                [value]="subcategory">
                {{subcategory}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <!-- Base Date Filter - Only for Purchase Dashboard -->
        <div class="filter-group" *ngIf="selectedDashboard === 'purchase'">
          <div class="filter-label">Base Date</div>
          <mat-form-field appearance="outline" class="filter-field">
            <mat-select [formControl]="baseDateCtrl" [disabled]="!isConfigLoaded || baseDateOptions.length === 0"
              placeholder="Select base date">
              <mat-option *ngIf="!isConfigLoaded" disabled>Loading...</mat-option>
              <mat-option *ngIf="isConfigLoaded && baseDateOptions.length === 0" disabled>Service
                unavailable</mat-option>
              <mat-option *ngFor="let baseDateOption of baseDateOptions" [value]="baseDateOption.value">
                {{baseDateOption.label}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <!-- Date Range Filters -->
        <div class="date-range-group">
          <div class="filter-group">
            <div class="filter-label">Start Date</div>
            <mat-form-field appearance="outline" class="filter-field">
              <input matInput [matDatepicker]="startPicker" [formControl]="startDate" placeholder="Start Date">
              <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
              <mat-datepicker #startPicker></mat-datepicker>
            </mat-form-field>
          </div>

          <div class="filter-group">
            <div class="filter-label">End Date</div>
            <mat-form-field appearance="outline" class="filter-field">
              <input matInput [matDatepicker]="endPicker" [formControl]="endDate" placeholder="End Date">
              <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
              <mat-datepicker #endPicker></mat-datepicker>
            </mat-form-field>
          </div>
        </div>

        <!-- Combined Mapping Configuration (Reconciliation Dashboard Only) -->
        <div class="filter-group" *ngIf="selectedDashboard === 'reconciliation'" style="padding-top: 5px;">
          <div class="mapping-config-section">
            <button mat-stroked-button
                    class="configure-mapping-btn"
                    (click)="openMappingConfiguration()"
                    [disabled]="!areMappingFiltersLoaded">
              <mat-icon>tune</mat-icon>
              Configure Mapping
            </button>
          </div>
        </div>


        <!-- Filter Action Buttons -->
        <div class="filter-actions">
          <button mat-stroked-button class="reset-filters-btn" (click)="resetFilters()">
            <mat-icon>refresh</mat-icon>
            Reset
          </button>
          <button mat-stroked-button color="primary" class="search-btn" (click)="searchDashboard()">
            <mat-icon>search</mat-icon>
            Search
          </button>
        </div>
      </div>

      <!-- Dashboard Mode Toggle -->
      <div class="dashboard-mode-section">
        <h4 class="mode-label">
          <mat-icon>settings</mat-icon>
          Dashboard Mode
        </h4>
        <div class="mode-toggle-container">
          <div class="toggle-switch" [class.ai-disabled]="isAiModeDisabled()">
            <div class="toggle-option" [class.active]="dashboardModeCtrl.value === 'default'"
              (click)="setDashboardMode('default')">
              <span>Default</span>
            </div>
            <div class="toggle-option ai-option" [class.active]="dashboardModeCtrl.value === 'ask_digi_ai'"
              [class.disabled]="isAiModeDisabled()" (click)="setDashboardMode('ask_digi_ai')">
              <span>Ask Digi AI</span>
              <mat-icon class="beta-icon" *ngIf="isAiModeDisabled()">🔥</mat-icon>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Content Area -->
    <div class="right-content">
      <!-- AI Assistant Header -->
      <div class="ai-assistant-header">
        <div class="assistant-info">
          <mat-icon class="assistant-icon disabled">smart_toy</mat-icon>
          <div class="assistant-text">
            <span class="assistant-title">Smart Dashboard Assistant</span>
            <span class="assistant-status disabled">Available Soon</span>
          </div>
        </div>
        <div class="search-container">
          <mat-form-field appearance="outline" class="search-field disabled">
            <input matInput placeholder="Ask me about your business data (Available Soon)"
              [formControl]="searchQuery" />
            <mat-icon matSuffix class="search-icon disabled">search</mat-icon>
          </mat-form-field>
        </div>
      </div>

      <!-- Configuration Warning (Reconciliation Dashboard Only) -->
      <div class="mapping-warning-container"
        *ngIf="selectedDashboard === 'reconciliation' && !isReconciliationMappingConfigured()">
        <div class="mapping-warning">
          <mat-icon class="warning-icon">warning</mat-icon>
          <div class="warning-content">
            <h4>Configuration Required</h4>
            <p>
              Please configure department-category mappings before generating the reconciliation report.
            </p>
          </div>
          <div class="warning-actions">
            <button mat-raised-button color="warn" (click)="openMappingConfiguration()">
              Configure Mapping
            </button>
          </div>
        </div>
      </div>

      <!-- Dashboard Content Area -->
      <div class="dashboard-content-area">
        <!-- Enhanced Loading State -->
        <div *ngIf="isLoading" class="loading-container">
          <div class="loading-content">
            <!-- Main Loading Animation -->
            <div class="loading-animation">
              <div class="pulse-circle"></div>
              <div class="pulse-circle delay-1"></div>
              <div class="pulse-circle delay-2"></div>
              <mat-icon class="loading-icon">analytics</mat-icon>
            </div>

            <!-- Loading Text -->
            <div class="loading-text">
              <h3>Preparing Your Dashboard</h3>
              <p>Analyzing your {{selectedDashboard}} data and generating insights...</p>
            </div>
          </div>
        </div>

        <!-- Dashboard Grid -->
        <div *ngIf="!isLoading && (summaryCards.length > 0 || charts.length > 0)" class="dashboard-grid">
          <!-- Summary Cards Section -->
          <div *ngIf="summaryCards.length > 0" class="dashboard-section">
            <div class="section-header">
              <div class="section-icon">
                <mat-icon>dashboard</mat-icon>
              </div>
              <h3 class="section-title">Key Metrics Overview</h3>
              <span class="section-subtitle">{{summaryCards.length}} metrics</span>
            </div>
            <div class="summary-cards-row">
              <mat-card *ngFor="let card of summaryCards" class="summary-card" [style.border-left-color]="card.color">
                <mat-card-content>
                  <div class="card-content">
                    <div class="card-icon"
                      [style.background]="'linear-gradient(135deg, ' + card.color + ' 0%, ' + card.color + '99 100%)'">
                      <mat-icon>{{card.icon}}</mat-icon>
                    </div>
                    <div class="card-info">
                      <div class="card-value">{{card.value}}</div>
                      <div class="card-label">{{card.label}}</div>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </div>

          <!-- Charts Section -->
          <div *ngIf="charts.length > 0" class="dashboard-section">
            <div class="charts-grid">
              <div *ngFor="let chart of charts; let i = index" class="chart-card" [ngClass]="getChartCssClass(chart)">
                <div *ngIf="chart.title">
                  <div class="chart-title">{{chart.title}}</div>
                </div>
                <div>
                  <div class="chart-container" [attr.data-chart-type]="chart.type">
                    <!-- Enhanced Table Chart Rendering -->
                    <div *ngIf="chart.type === 'table'" class="table-chart">
                      <div class="table-responsive">
                        <!-- Search and Export Controls -->
                        <div class="table-controls"
                          *ngIf="getTableOptions(chart)?.searchable || getTableOptions(chart)?.exportable">
                          <div class="table-search" *ngIf="getTableOptions(chart)?.searchable">
                            <mat-form-field appearance="outline" class="search-field">
                              <input matInput (keyup)="applyTableFilter($event, chart)" placeholder="Search table...">
                              <mat-icon matSuffix>search</mat-icon>
                            </mat-form-field>
                          </div>
                          <div class="table-export" *ngIf="getTableOptions(chart)?.exportable">
                            <button mat-raised-button color="primary" (click)="exportTable(chart)">
                              <mat-icon>download</mat-icon>
                              Export
                            </button>
                          </div>
                        </div>

                        <!-- Material Table -->
                        <mat-table [dataSource]="getTableDataSource(chart)" class="reconciliation-mat-table"
                          style="padding: 4px;">
                          <!-- Dynamic columns -->
                          <ng-container *ngFor="let header of getTableHeaders(chart); let i = index"
                            [matColumnDef]="'col' + i">
                            <mat-header-cell *matHeaderCellDef>{{header}}</mat-header-cell>
                            <mat-cell *matCellDef="let row">
                              <span [innerHTML]="formatTableCell(row[header], header, row)"
                                [class.category-cell]="row._isCategory" [class.grand-total-cell]="row._isGrandTotal">
                              </span>
                            </mat-cell>
                          </ng-container>

                          <mat-header-row *matHeaderRowDef="getDisplayedColumns(chart)"></mat-header-row>
                          <mat-row *matRowDef="let row; columns: getDisplayedColumns(chart);"
                            [class.category-row]="row._isCategory" [class.subcategory-row]="row._isSubcategory"
                            [class.grand-total-row]="row._isGrandTotal"></mat-row>
                        </mat-table>

                        <!-- Pagination -->
                        <mat-paginator
                          *ngIf="getTableOptions(chart)?.showPagination && getTableRows(chart).length > (getTableOptions(chart)?.pageSize || 50)"
                          [length]="getTableRows(chart).length" [pageSize]="getTableOptions(chart)?.pageSize || 50"
                          [pageSizeOptions]="[25, 50, 100, 200]" showFirstLastButtons>
                        </mat-paginator>
                      </div>
                    </div>

                    <!-- Regular Chart Rendering -->
                    <canvas *ngIf="chart.type !== 'table'" baseChart [type]="getChartType(chart)"
                      [data]="getChartData(chart)" [options]="getChartOptions(chart)">
                    </canvas>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Single Unified Empty State -->
        <div *ngIf="!isLoading && summaryCards.length === 0 && charts.length === 0" class="empty-state">
          <mat-icon class="empty-icon">analytics</mat-icon>
          <h3 *ngIf="dashboardTypes.length > 0 && selectedDashboard">No Data Available</h3>
          <h3 *ngIf="dashboardTypes.length === 0 || !selectedDashboard">Service Unavailable</h3>
          <p *ngIf="dashboardTypes.length > 0 && selectedDashboard">Please select filters and click the Search button to
            view dashboard data.</p>
          <p *ngIf="dashboardTypes.length === 0 || !selectedDashboard">Dashboard configuration could not be loaded.
            Please check your connection and try again.</p>
          <button *ngIf="dashboardTypes.length > 0 && selectedDashboard" mat-raised-button color="primary"
            (click)="searchDashboard()">
            <mat-icon>refresh</mat-icon>
            Refresh
          </button>
          <button *ngIf="dashboardTypes.length === 0 || !selectedDashboard" mat-raised-button color="primary"
            (click)="window.location.reload()">
            <mat-icon>refresh</mat-icon>
            Reload Page
          </button>
        </div>
      </div>
    </div>
  </div>
</div>





<!-- Combined Mapping Configuration Stepper Dialog -->
<div *ngIf="showMappingConfigurationStepper" class="mapping-stepper-overlay">
  <div class="mapping-stepper-dialog">
    <div class="stepper-header">
      <h2>
        <mat-icon>tune</mat-icon>
        Configure Mapping
      </h2>
      <button mat-icon-button (click)="closeMappingConfiguration()" class="close-btn">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <mat-stepper #stepper linear="false" class="mapping-stepper">
      <!-- Step 1: Category-WorkArea Mapping -->
      <mat-step label="Work Area Mapping" [completed]="isCategoryWorkareaMappingConfigured()">
        <div class="step-content">
          <div class="step-description">
            <h3>Configure Work Area Mappings</h3>
            <p>Map categories to their respective work areas for inventory tracking.</p>
          </div>

          <div class="step-mapping-content">
            <!-- Category Selection Section -->
            <div class="category-selection-section">
              <div class="section-header">
                <mat-icon>category</mat-icon>
                <h4>Select Categories</h4>
              </div>

              <!-- Category Selection -->
              <mat-form-field *ngIf="categories.length > 0" appearance="outline" class="category-filter-field">
                <mat-select [formControl]="selectedCategoriesForMappingCtrl" multiple
                  [placeholder]="'Select categories (' + (selectedCategoriesForMappingCtrl.value?.length || 0) + '/' + filteredCategories.length + ')'">
                  <mat-option>
                    <ngx-mat-select-search [formControl]="categoryFilterCtrl" placeholderLabel="Search categories..."
                      noEntriesFoundLabel="No categories found">
                    </ngx-mat-select-search>
                  </mat-option>
                  <!-- Select All / Deselect All Options -->
                  <div class="select-all-custom-option" (click)="toggleAllCategoriesForMapping($event)">
                    <strong>{{areAllCategoriesSelectedForMapping() ? 'Deselect All' : 'Select All'}}</strong>
                  </div>
                  <mat-divider></mat-divider>
                  <mat-option *ngFor="let category of filteredCategories; trackBy: trackByCategory" [value]="category">
                    {{category}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <!-- WorkArea Mapping Section -->
            <div class="workarea-mapping-section">
              <div class="section-header">
                <mat-icon>work</mat-icon>
                <h4>Configure Work Area Mappings as per Inventory</h4>
              </div>
              <app-category-workarea-mapping
                [categories]="categories"
                [selectedCategoryNames]="selectedCategoriesForMappingCtrl.value || []"
                [workAreas]="getWorkAreasForMapping()"
                [existingMappings]="categoryWorkareaMappings"
                [tenantId]="user?.tenantId"
                [showAsDialog]="false"
                (mappingsChanged)="onCategoryWorkareaMappingsChanged($event)">
              </app-category-workarea-mapping>
            </div>
          </div>

          <div class="step-actions">
            <button mat-button matStepperNext [disabled]="!isCategoryWorkareaMappingConfigured()">
              Next: Department Mapping
              <mat-icon>arrow_forward</mat-icon>
            </button>
          </div>
        </div>
      </mat-step>

      <!-- Step 2: Department-Category Mapping -->
      <mat-step label="Department Mapping" [completed]="isDepartmentCategoryMappingConfigured()">
        <div class="step-content">
          <div class="step-description">
            <h3>Configure Department Mappings</h3>
            <p>Map departments to their respective categories for sales analysis.</p>
          </div>

          <div class="step-mapping-content">
            <!-- Department Selection Section -->
            <div class="department-selection-section">
              <div class="section-header">
                <mat-icon>business</mat-icon>
                <h4>Select Departments(POS)</h4>
              </div>

              <!-- Department Selection -->
              <mat-form-field *ngIf="departments.length > 0" appearance="outline" class="department-filter-field">
                <mat-select [formControl]="selectedDepartmentsCtrl" multiple
                  [placeholder]="'Select departments (' + (selectedDepartmentsCtrl.value?.length || 0) + '/' + filteredDepartments.length + ')'">
                  <mat-option>
                    <ngx-mat-select-search [formControl]="departmentFilterCtrl" placeholderLabel="Search departments..."
                      noEntriesFoundLabel="No departments found">
                    </ngx-mat-select-search>
                  </mat-option>
                  <!-- Select All / Deselect All Options -->
                  <div class="select-all-custom-option" (click)="toggleAllDepartments($event)">
                    <strong>{{areAllDepartmentsSelected() ? 'Deselect All' : 'Select All'}}</strong>
                  </div>
                  <mat-divider></mat-divider>
                  <mat-option *ngFor="let department of filteredDepartments; trackBy: trackByIndex" [value]="department.id">
                    {{department.name}} <span *ngIf="department.code">({{department.code}})</span>
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <!-- Category Mapping Section -->
            <div class="category-mapping-section">
              <div class="section-header">
                <mat-icon>category</mat-icon>
                <h4>Configure Category Mappings as per Inventory</h4>
              </div>
              <app-department-category-mapping [departments]="departments"
                [selectedDepartmentIds]="selectedDepartmentsCtrl.value || []" [categories]="categories"
                [existingMappings]="departmentCategoryMappings" [tenantId]="user?.tenantId" [showAsDialog]="false"
                (mappingsChanged)="onDepartmentMappingsChanged($event)">
              </app-department-category-mapping>
            </div>
          </div>

          <div class="step-actions">
            <button mat-button matStepperPrevious>
              <mat-icon>arrow_back</mat-icon>
              Previous: Work Area
            </button>
            <button mat-raised-button color="primary" (click)="completeMappingConfiguration()"
              [disabled]="!isDepartmentCategoryMappingConfigured()">
              Complete Configuration
              <mat-icon>check</mat-icon>
            </button>
          </div>
        </div>
      </mat-step>
    </mat-stepper>
  </div>
</div>